/**
 * DatabaseService.js
 * SQLite database service for CoreDesk Framework
 */

const { app } = require('electron');
const path = require('path');

class DatabaseService {
    constructor(logger) {
        this.logger = logger;
        this.db = null;
        this.dbPath = null;
        this.isInitialized = false;
    }

    async initialize() {
        try {
            const sqlite3 = require('sqlite3').verbose();
            
            // Get database path
            this.dbPath = path.join(app.getPath('userData'), 'coredesk.db');
            this.logger.info('DatabaseService', 'Database path configured', { path: this.dbPath });

            // Create database connection
            this.db = await this.createConnection(sqlite3);
            
            // Configure database
            this.configureDatabase();
            
            // Create tables if they don't exist
            await this.createTables();
            
            this.isInitialized = true;
            this.logger.info('DatabaseService', 'Database service initialized successfully');
            
            return true;

        } catch (error) {
            this.logger.error('DatabaseService', 'Database initialization failed', error);
            return false;
        }
    }

    createConnection(sqlite3) {
        return new Promise((resolve, reject) => {
            const db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    this.logger.error('DatabaseService', 'Database connection error', err);
                    reject(err);
                } else {
                    this.logger.info('DatabaseService', 'Connected to SQLite database');
                    resolve(db);
                }
            });
        });
    }

    configureDatabase() {
        if (!this.db) return;

        // Configure database settings for optimal performance
        this.db.serialize(() => {
            this.db.run('PRAGMA journal_mode=WAL');
            this.db.run('PRAGMA foreign_keys=ON');
            this.db.run('PRAGMA synchronous=NORMAL');
            this.db.run('PRAGMA page_size=4096');
            this.db.run('PRAGMA cache_size=-64000');
        });

        this.logger.debug('DatabaseService', 'Database configuration applied');
    }

    async createTables() {
        const tables = this.getTableDefinitions();
        
        for (const [tableName, sql] of Object.entries(tables)) {
            try {
                await this.executeQuery(sql);
                this.logger.debug('DatabaseService', `Table ${tableName} created/verified`);
            } catch (error) {
                this.logger.error('DatabaseService', `Failed to create table ${tableName}`, error);
                throw error;
            }
        }
    }

    getTableDefinitions() {
        return {
            users: `
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT,
                    role TEXT DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    last_login DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `,
            licenses: `
                CREATE TABLE IF NOT EXISTS licenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    license_key TEXT UNIQUE NOT NULL,
                    email TEXT NOT NULL,
                    type TEXT NOT NULL DEFAULT 'trial',
                    status TEXT DEFAULT 'inactive',
                    device_fingerprint TEXT,
                    activated_at DATETIME,
                    expires_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `,
            settings: `
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,
                    key TEXT NOT NULL,
                    value TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(category, key)
                )
            `,
            sync_queue: `
                CREATE TABLE IF NOT EXISTS sync_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operation_type TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    record_id TEXT,
                    data TEXT,
                    status TEXT DEFAULT 'pending',
                    retry_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    processed_at DATETIME
                )
            `,
            audit_log: `
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    table_name TEXT,
                    record_id TEXT,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            `,
            installed_modules: `
                CREATE TABLE IF NOT EXISTS installed_modules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    module_id TEXT NOT NULL UNIQUE,
                    name TEXT NOT NULL,
                    version TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    install_path TEXT NOT NULL,
                    manifest_data TEXT,
                    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    CHECK (status IN ('active', 'inactive'))
                )
            `
        };
    }

    executeQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            this.db.run(sql, params, function(err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({
                        lastID: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }

    executeSelect(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    executeSelectOne(sql, params = []) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('Database not initialized'));
                return;
            }

            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row || null);
                }
            });
        });
    }

    getConnection() {
        return this.db;
    }

    async backup(backupPath) {
        try {
            if (!this.db) {
                throw new Error('Database not initialized');
            }

            // Create backup using SQLite backup API
            // This is a simplified version - full implementation would use sqlite3 backup API
            this.logger.info('DatabaseService', 'Database backup requested', { path: backupPath });
            
            // TODO: Implement actual backup logic
            return true;

        } catch (error) {
            this.logger.error('DatabaseService', 'Database backup failed', error);
            return false;
        }
    }

    async getStats() {
        try {
            const tables = ['users', 'licenses', 'settings', 'sync_queue', 'audit_log'];
            const stats = {};

            for (const table of tables) {
                const result = await this.executeSelectOne(`SELECT COUNT(*) as count FROM ${table}`);
                stats[table] = result ? result.count : 0;
            }

            return {
                tables: stats,
                dbPath: this.dbPath,
                isInitialized: this.isInitialized
            };

        } catch (error) {
            this.logger.error('DatabaseService', 'Failed to get database stats', error);
            return null;
        }
    }

    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    this.logger.error('DatabaseService', 'Error closing database', err);
                } else {
                    this.logger.info('DatabaseService', 'Database connection closed');
                }
            });
            this.db = null;
            this.isInitialized = false;
        }
    }
}

module.exports = DatabaseService;