/**
 * ModulePersistenceClient
 * Client-side wrapper for ModulePersistenceService that works through IPC
 * Provides the same interface as ModulePersistenceService but communicates with main process
 */

class ModulePersistenceClient {
    constructor(logger) {
        this.logger = logger || console;
        this.isInitialized = false;
        
        // Cache for frequently accessed data
        this.moduleCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = 0;
    }

    /**
     * Initialize the client
     * @returns {Promise<boolean>}
     */
    async initialize() {
        try {
            if (!window.electronAPI || !window.electronAPI.database) {
                throw new Error('ElectronAPI database interface not available');
            }

            this.logger.info('ModulePersistenceClient', 'Initializing module persistence client...');
            
            // Test database connection
            await this.testConnection();
            
            // Load initial cache
            await this.refreshCache();
            
            this.isInitialized = true;
            this.logger.info('ModulePersistenceClient', 'Module persistence client initialized successfully');
            
            return true;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to initialize:', error);
            return false;
        }
    }

    /**
     * Test database connection
     * @private
     */
    async testConnection() {
        try {
            const sql = `
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='installed_modules'
            `;
            
            const result = await window.electronAPI.database.execute(sql, []);
            
            if (!result || !result.data || result.data.length === 0) {
                throw new Error('installed_modules table does not exist');
            }
            
            this.logger.debug('ModulePersistenceClient', 'Database connection test successful');
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Database connection test failed:', error);
            throw error;
        }
    }

    /**
     * Register a module as installed
     * @param {Object} moduleData - Module data to register
     * @returns {Promise<boolean>}
     */
    async registerInstalledModule(moduleData) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            const { moduleId, name, version, installPath, manifestData, status = 'active' } = moduleData;
            
            if (!moduleId || !name || !version || !installPath) {
                throw new Error('Missing required module data');
            }

            this.logger.info('ModulePersistenceClient', `Registering module: ${moduleId}@${version}`);

            // Check if module already exists
            const existingModule = await this.getInstalledModule(moduleId);
            
            if (existingModule) {
                // Update existing module
                const sql = `
                    UPDATE installed_modules 
                    SET name = ?, version = ?, status = ?, install_path = ?, 
                        manifest_data = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE module_id = ?
                `;
                
                const result = await window.electronAPI.database.execute(sql, [
                    name, version, status, installPath, 
                    JSON.stringify(manifestData), moduleId
                ]);
                
                if (result.success) {
                    this.logger.info('ModulePersistenceClient', `Updated existing module: ${moduleId}`);
                } else {
                    throw new Error(`Failed to update module: ${result.error}`);
                }
            } else {
                // Insert new module
                const sql = `
                    INSERT INTO installed_modules 
                    (module_id, name, version, status, install_path, manifest_data)
                    VALUES (?, ?, ?, ?, ?, ?)
                `;
                
                const result = await window.electronAPI.database.execute(sql, [
                    moduleId, name, version, status, installPath, 
                    JSON.stringify(manifestData)
                ]);
                
                if (result.success) {
                    this.logger.info('ModulePersistenceClient', `Registered new module: ${moduleId}`);
                } else {
                    throw new Error(`Failed to register module: ${result.error}`);
                }
            }

            // Refresh cache
            await this.refreshCache();
            
            return true;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to register module ${moduleData?.moduleId}:`, error);
            return false;
        }
    }

    /**
     * Unregister a module
     * @param {string} moduleId - Module ID to unregister
     * @returns {Promise<boolean>}
     */
    async unregisterModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            this.logger.info('ModulePersistenceClient', `Unregistering module: ${moduleId}`);

            const sql = 'DELETE FROM installed_modules WHERE module_id = ?';
            const result = await window.electronAPI.database.execute(sql, [moduleId]);
            
            if (result.success) {
                this.logger.info('ModulePersistenceClient', `Successfully unregistered module: ${moduleId}`);
                
                // Refresh cache
                await this.refreshCache();
                
                return true;
            } else {
                this.logger.warn('ModulePersistenceClient', `Failed to unregister module: ${result.error}`);
                return false;
            }
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to unregister module ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Get all installed modules
     * @returns {Promise<Array>}
     */
    async getInstalledModules() {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            // Use cache if available and fresh
            if (this.isCacheValid()) {
                return Array.from(this.moduleCache.values());
            }

            const sql = 'SELECT * FROM installed_modules ORDER BY installed_at DESC';
            const result = await window.electronAPI.database.execute(sql, []);
            
            if (!result.success) {
                throw new Error(`Database query failed: ${result.error}`);
            }

            const modules = result.data || [];
            
            // Parse manifest data
            const parsedModules = modules.map(module => ({
                ...module,
                manifest_data: module.manifest_data ? JSON.parse(module.manifest_data) : null
            }));

            // Update cache
            this.updateCache(parsedModules);
            
            return parsedModules;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get installed modules:', error);
            return [];
        }
    }

    /**
     * Get a specific installed module
     * @param {string} moduleId - Module ID
     * @returns {Promise<Object|null>}
     */
    async getInstalledModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            // Check cache first
            if (this.isCacheValid() && this.moduleCache.has(moduleId)) {
                return this.moduleCache.get(moduleId);
            }

            const sql = 'SELECT * FROM installed_modules WHERE module_id = ?';
            const result = await window.electronAPI.database.execute(sql, [moduleId]);
            
            if (!result.success) {
                throw new Error(`Database query failed: ${result.error}`);
            }

            const modules = result.data || [];
            
            if (modules.length > 0) {
                const module = modules[0];
                // Parse manifest data
                const parsedModule = {
                    ...module,
                    manifest_data: module.manifest_data ? JSON.parse(module.manifest_data) : null
                };
                
                // Update cache entry
                this.moduleCache.set(moduleId, parsedModule);
                
                return parsedModule;
            }
            
            return null;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to get module ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Check if a module is installed
     * @param {string} moduleId - Module ID
     * @returns {Promise<boolean>}
     */
    async isModuleInstalled(moduleId) {
        try {
            const module = await this.getInstalledModule(moduleId);
            return module !== null;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to check if module ${moduleId} is installed:`, error);
            return false;
        }
    }

    /**
     * Update module status
     * @param {string} moduleId - Module ID
     * @param {string} status - New status ('active' or 'inactive')
     * @returns {Promise<boolean>}
     */
    async updateModuleStatus(moduleId, status) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            if (!['active', 'inactive'].includes(status)) {
                throw new Error('Invalid status. Must be "active" or "inactive"');
            }

            this.logger.info('ModulePersistenceClient', `Updating module ${moduleId} status to: ${status}`);

            const sql = `
                UPDATE installed_modules 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE module_id = ?
            `;
            
            const result = await window.electronAPI.database.execute(sql, [status, moduleId]);
            
            if (result.success) {
                this.logger.info('ModulePersistenceClient', `Successfully updated module ${moduleId} status`);
                
                // Refresh cache
                await this.refreshCache();
                
                return true;
            } else {
                this.logger.warn('ModulePersistenceClient', `Failed to update module status: ${result.error}`);
                return false;
            }
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to update module ${moduleId} status:`, error);
            return false;
        }
    }

    /**
     * Get active modules only
     * @returns {Promise<Array>}
     */
    async getActiveModules() {
        try {
            const allModules = await this.getInstalledModules();
            return allModules.filter(module => module.status === 'active');
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get active modules:', error);
            return [];
        }
    }

    /**
     * Refresh cache
     * @private
     */
    async refreshCache() {
        try {
            const sql = 'SELECT * FROM installed_modules';
            const result = await window.electronAPI.database.execute(sql, []);
            
            if (!result.success) {
                throw new Error(`Database query failed: ${result.error}`);
            }

            const modules = result.data || [];
            
            // Parse and update cache
            this.moduleCache.clear();
            modules.forEach(module => {
                const parsedModule = {
                    ...module,
                    manifest_data: module.manifest_data ? JSON.parse(module.manifest_data) : null
                };
                this.moduleCache.set(module.module_id, parsedModule);
            });
            
            this.lastCacheUpdate = Date.now();
            this.logger.debug('ModulePersistenceClient', `Cache refreshed with ${modules.length} modules`);
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to refresh cache:', error);
        }
    }

    /**
     * Update cache with modules array
     * @private
     */
    updateCache(modules) {
        this.moduleCache.clear();
        modules.forEach(module => {
            this.moduleCache.set(module.module_id, module);
        });
        this.lastCacheUpdate = Date.now();
    }

    /**
     * Check if cache is valid
     * @private
     */
    isCacheValid() {
        return (Date.now() - this.lastCacheUpdate) < this.cacheExpiry;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.moduleCache.clear();
        this.lastCacheUpdate = 0;
        this.logger.debug('ModulePersistenceClient', 'Cache cleared');
    }
}

// Export for browser environment
if (typeof window !== 'undefined') {
    window.ModulePersistenceClient = ModulePersistenceClient;
}
