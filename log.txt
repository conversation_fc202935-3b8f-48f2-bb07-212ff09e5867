Failed to load resource: net::ERR_FILE_NOT_FOUND
leftPanel-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
rightPanel-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
bottomPanel-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
folder-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
cloud-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
search-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
grid-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
puzzle-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
settings-mask.svg:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
FileExplorer.js:543 FileExplorer Directory listing failed: Object
loadRealFileList @ FileExplorer.js:543
ModuleRegistry.js:46 [ModulePersistenceClient] Database connection test failed: Error: Error invoking remote method 'db-execute': TypeError: this.database.execute is not a function
error @ ModuleRegistry.js:46
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to initialize: Error: Error invoking remote method 'db-execute': TypeError: this.database.execute is not a function
error @ ModuleRegistry.js:46
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:181:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:324:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at async ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:13)
    at async ModuleRegistry.initialize (ModuleRegistry.js:64:17)
    at async DynamicModuleManager.createDefaultRegistry (DynamicModuleManager.js:886:13)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:85:54)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ ModuleRegistry.js:46
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:181:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:324:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:24)
    at ModuleRegistry.initialize (ModuleRegistry.js:64:28)
    at async DynamicModuleManager.initialize (DynamicModuleManager.js:92:13)
    at async ExclusiveModuleController.initializeDynamicSystem (ExclusiveModuleController.js:75:13)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:37:9)
error @ ModuleRegistry.js:46
DynamicModuleManager.js:48 [ModulePersistenceClient] Database connection test failed: Error: Error invoking remote method 'db-execute': TypeError: this.database.execute is not a function
error @ DynamicModuleManager.js:48
DynamicModuleManager.js:48 [ModulePersistenceClient] Failed to initialize: Error: Error invoking remote method 'db-execute': TypeError: this.database.execute is not a function
error @ DynamicModuleManager.js:48
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:181:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:324:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at ModuleRegistry.loadPersistedData (ModuleRegistry.js:510:24)
    at ModuleRegistry.initialize (ModuleRegistry.js:64:28)
    at async DynamicModuleManager.refreshRegistry (DynamicModuleManager.js:724:13)
    at async ExclusiveModuleController.initializeAvailableModules (ExclusiveModuleController.js:94:9)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:40:9)
error @ ModuleRegistry.js:46
ModuleRegistry.js:46 [ModulePersistenceClient] Failed to get installed modules: Error: Client not initialized
    at ModulePersistenceClient.getInstalledModules (ModulePersistenceClient.js:181:23)
    at ModulePersistenceClient.getActiveModules (ModulePersistenceClient.js:324:43)
    at ModuleRegistry.loadInstalledModulesFromStorage (ModuleRegistry.js:538:75)
    at ExclusiveModuleController.loadModulesFromFilesystem (ExclusiveModuleController.js:115:32)
    at ExclusiveModuleController.initializeAvailableModules (ExclusiveModuleController.js:103:24)
    at async ExclusiveModuleController.initialize (ExclusiveModuleController.js:40:9)
error @ ModuleRegistry.js:46